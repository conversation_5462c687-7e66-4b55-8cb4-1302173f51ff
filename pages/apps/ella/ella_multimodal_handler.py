"""
Ella多模态功能处理器
处理文档、图片、相机、AI生图等多模态功能的业务流程
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from core.logger import log
from config.multimodal_config import (
    get_timeout, get_retry_count, get_function_config,
    get_camera_shutter_selectors, get_camera_confirm_selectors,
    get_file_confirm_buttons, get_gallery_confirm_buttons,
    should_continue_on_error, is_debug_enabled
)
from tools.file_pusher import file_pusher


class EllaMultimodalHandler:
    """Ella多模态功能处理器"""

    def __init__(self, driver, page_elements):
        """
        初始化多模态处理器
        
        Args:
            driver: UI自动化驱动器
            page_elements: 页面元素字典
        """
        self.driver = driver
        self.page_elements = page_elements

    def execute_multimodal_function(self, model: str) -> bool:
        """
        根据模型类型执行对应的多模态功能
        
        Args:
            model: 功能模型类型 (document/gallery/camera/ai_image_generator)
            
        Returns:
            bool: 执行是否成功
        """
        try:
            log.info(f"执行多模态功能: {model}")
            
            # 首先点击多模态入口
            if not self._open_multimodal_entrance():
                return False
            
            # 根据模型类型执行对应功能
            if model == "document":
                return self._handle_document_flow()
            elif model == "gallery":
                return self._handle_gallery_flow()
            elif model == "camera":
                return self._handle_camera_flow()
            elif model == "ai_image_generator":
                return self._handle_ai_image_generator_flow()
            else:
                log.error(f"不支持的模型类型: {model}")
                return False
                
        except Exception as e:
            log.error(f"执行多模态功能失败: {e}")
            return False

    def _open_multimodal_entrance(self) -> bool:
        """打开多模态入口"""
        try:
            log.info("点击多模态入口")
            
            # 获取多模态入口按钮
            entrance_button = self.page_elements.get('multi_modal_entrance')
            if not entrance_button:
                log.error("未找到多模态入口按钮元素")
                return False
            
            if entrance_button.is_exists():
                entrance_button.click()
                time.sleep(get_timeout("entrance_open"))  # 等待菜单展开
                log.info("✅ 成功点击多模态入口")
                return True
            else:
                log.error("多模态入口按钮不存在")
                return False
                
        except Exception as e:
            log.error(f"打开多模态入口失败: {e}")
            return False

    def _handle_document_flow(self) -> bool:
        """
        处理文档功能流程
        推送测试文档->点击document按钮->选择文件
        """
        try:
            log.info("执行文档功能流程")

            # 0. 推送测试文档到设备
            if not self._prepare_test_documents():
                log.warning("推送测试文档失败，但继续执行")

            # 1. 点击document按钮
            document_button = self.page_elements.get('multi_modal_document')
            if not document_button or not document_button.is_exists():
                log.error("未找到document按钮")
                return False

            document_button.click()
            time.sleep(get_timeout("button_click"))
            log.info("✅ 点击document按钮成功")

            # 2. 选择文件 (文件选择完成后会自动返回)
            if not self._select_file():
                log.warning("文件选择可能失败")
                return should_continue_on_error("selection_failure")

            log.info("✅ 文档功能流程完成")
            return True

        except Exception as e:
            log.error(f"文档功能流程失败: {e}")
            return False

    def _handle_gallery_flow(self) -> bool:
        """
        处理图库功能流程
        推送测试图片->点击gallery->选中照片
        """
        try:
            log.info("执行图库功能流程")

            # 0. 推送测试图片到设备
            if not self._prepare_test_images():
                log.warning("推送测试图片失败，但继续执行")

            # 1. 点击gallery按钮
            gallery_button = self.page_elements.get('multi_modal_gallery')
            if not gallery_button or not gallery_button.is_exists():
                log.error("未找到gallery按钮")
                return False

            gallery_button.click()
            time.sleep(get_timeout("app_launch"))
            log.info("✅ 点击gallery按钮成功")

            # 2. 选中照片 (照片选择完成后会自动返回)
            if not self._select_photo():
                log.warning("照片选择可能失败")
                return should_continue_on_error("selection_failure")

            log.info("✅ 图库功能流程完成")
            return True

        except Exception as e:
            log.error(f"图库功能流程失败: {e}")
            return False

    def _handle_camera_flow(self) -> bool:
        """
        处理相机功能流程
        点击camera->拍照->确认照片
        """
        try:
            log.info("执行相机功能流程")

            # 1. 点击camera按钮
            camera_button = self.page_elements.get('multi_modal_camera')
            if not camera_button or not camera_button.is_exists():
                log.error("未找到camera按钮")
                return False

            camera_button.click()
            time.sleep(get_timeout("button_click"))
            log.info("✅ 点击camera按钮成功")

            # 2. 等待相机应用启动
            time.sleep(get_timeout("app_launch"))

            # 3. 拍照
            if not self._take_photo():
                log.warning("拍照可能失败")
                return should_continue_on_error("camera_operation")

            # 4. 确认照片 (确认后会自动返回)
            if not self._confirm_photo():
                log.warning("照片确认可能失败")
                return should_continue_on_error("camera_operation")

            log.info("✅ 相机功能流程完成")
            return True

        except Exception as e:
            log.error(f"相机功能流程失败: {e}")
            return False

    def _handle_ai_image_generator_flow(self) -> bool:
        """
        处理AI生图功能流程
        点击ai_image_generator->配置类型和比例
        """
        try:
            log.info("执行AI生图功能流程")

            # 1. 点击ai_image_generator按钮
            ai_generator_button = self.page_elements.get('multi_modal_ai_image_generator')
            if not ai_generator_button or not ai_generator_button.is_exists():
                log.error("未找到ai_image_generator按钮")
                return False

            ai_generator_button.click()
            time.sleep(get_timeout("button_click"))
            log.info("✅ 点击ai_image_generator按钮成功")

            # 2. 配置AI生图类型 (可选)
            type_button = self.page_elements.get('multi_modal_ai_image_generator_type')
            if type_button and type_button.is_exists():
                type_button.click()
                time.sleep(get_timeout("ai_generator_setup"))
                log.info("✅ 配置ai_image_generator_type成功")
            else:
                log.info("ai_image_generator_type按钮不存在，跳过配置")

            # 3. 配置AI生图比例 (可选)
            scale_button = self.page_elements.get('multi_modal_ai_image_generator_scale')
            if scale_button and scale_button.is_exists():
                scale_button.click()
                time.sleep(get_timeout("ai_generator_setup"))
                log.info("✅ 配置ai_image_generator_scale成功")
            else:
                log.info("ai_image_generator_scale按钮不存在，跳过配置")

            log.info("✅ AI生图功能流程完成")
            return True

        except Exception as e:
            log.error(f"AI生图功能流程失败: {e}")
            return False

    def _select_file(self) -> bool:
        """选择文件"""
        try:
            log.info("尝试选择文件")

            # 等待文件选择器出现
            time.sleep(get_timeout("file_selection"))

            # 尝试点击第一个文件
            file_items = self.driver(className="android.widget.TextView")
            if file_items.exists(timeout=get_timeout("file_selection")):
                # 选择第一个可见的文件项
                if len(file_items) > 0:
                    file_items[0].click()
                    time.sleep(get_timeout("button_click"))
                    log.info("✅ 选择文件成功")

                # 尝试点击确认按钮 (如果需要)
                confirm_buttons = get_file_confirm_buttons()
                for btn_text in confirm_buttons:
                    btn = self.driver(text=btn_text)
                    if btn.exists(timeout=2):
                        btn.click()
                        log.info(f"✅ 点击{btn_text}按钮确认文件选择")
                        break

                return True
            else:
                log.warning("未找到文件项，但文件选择可能已完成")
                return True

        except Exception as e:
            log.error(f"选择文件失败: {e}")
            return False

    def _select_photo(self) -> bool:
        """选择照片"""
        try:
            log.info("尝试选择照片")

            # 等待图库界面加载
            time.sleep(get_timeout("photo_selection"))

            # 尝试点击第一张照片
            photos = self.driver(className="android.widget.ImageView")
            if photos.exists(timeout=get_timeout("photo_selection")):
                # 选择第一张照片
                if len(photos) > 0:
                    photos[0].click()
                    time.sleep(get_timeout("button_click"))
                    log.info("✅ 选择照片成功")

                # 尝试点击确认按钮 (如果需要)
                confirm_buttons = get_gallery_confirm_buttons()
                for btn_text in confirm_buttons:
                    btn = self.driver(text=btn_text)
                    if btn.exists(timeout=2):
                        btn.click()
                        log.info(f"✅ 点击{btn_text}按钮确认照片选择")
                        break

                return True
            else:
                log.warning("未找到照片项，但照片选择可能已完成")
                return True

        except Exception as e:
            log.error(f"选择照片失败: {e}")
            return False

    def _take_photo(self) -> bool:
        """拍照"""
        try:
            log.info("尝试拍照")

            # 等待相机界面加载
            time.sleep(get_timeout("camera_ready"))

            # 尝试找到拍照按钮并点击
            shutter_selectors = get_camera_shutter_selectors()

            for selector in shutter_selectors:
                shutter_btn = self.driver(**selector)
                if shutter_btn.exists(timeout=get_timeout("camera_ready")):
                    shutter_btn.click()
                    time.sleep(get_timeout("photo_capture"))
                    log.info("✅ 拍照成功")
                    return True

            # 如果找不到特定按钮，尝试点击屏幕下方中央（通常是拍照按钮位置）
            try:
                width, height = self.driver.window_size()
                self.driver.click(width // 2, int(height * 0.85))
                time.sleep(get_timeout("photo_capture"))
                log.info("✅ 通过坐标点击拍照成功")
                return True
            except Exception as e:
                log.warning(f"坐标点击拍照失败: {e}")
                return False

        except Exception as e:
            log.error(f"拍照失败: {e}")
            return False

    def _confirm_photo(self) -> bool:
        """确认照片"""
        try:
            log.info("尝试确认照片")

            # 等待照片预览界面
            time.sleep(get_timeout("photo_confirm"))

            # 尝试找到确认按钮
            confirm_selectors = get_camera_confirm_selectors()

            for selector in confirm_selectors:
                confirm_btn = self.driver(**selector)
                if confirm_btn.exists(timeout=get_timeout("photo_confirm")):
                    confirm_btn.click()
                    time.sleep(get_timeout("button_click"))
                    log.info("✅ 确认照片成功")
                    return True

            # 如果没有找到确认按钮，可能照片已自动保存
            log.info("未找到确认按钮，照片可能已自动保存")
            return True

        except Exception as e:
            log.error(f"确认照片失败: {e}")
            return False

    def _prepare_test_documents(self) -> bool:
        """准备测试文档"""
        try:
            log.info("开始推送测试文档到设备")

            # 检查设备连接
            if not file_pusher.check_device_connection():
                log.error("设备未连接，无法推送测试文档")
                return False

            # 推送文档到Download目录
            target_path = "/sdcard/Download"
            if file_pusher.push_documents_to_device(target_path):
                log.info("✅ 测试文档推送成功")

                # 等待文件系统同步
                time.sleep(get_timeout("button_click"))

                # 验证文件是否推送成功
                files = file_pusher.list_device_files(target_path)
                if files:
                    log.info(f"设备中的文件: {files}")

                return True
            else:
                log.error("❌ 测试文档推送失败")
                return False

        except Exception as e:
            log.error(f"准备测试文档失败: {e}")
            return False

    def _prepare_test_images(self) -> bool:
        """准备测试图片"""
        try:
            log.info("开始推送测试图片到设备")

            # 检查设备连接
            if not file_pusher.check_device_connection():
                log.error("设备未连接，无法推送测试图片")
                return False

            # 推送图片到Pictures目录
            target_path = "/sdcard/Pictures"
            if file_pusher.push_images_to_device(target_path):
                log.info("✅ 测试图片推送成功")

                # 等待文件系统同步
                time.sleep(get_timeout("button_click"))

                # 验证文件是否推送成功
                files = file_pusher.list_device_files(target_path)
                if files:
                    log.info(f"设备中的图片: {files}")

                return True
            else:
                log.error("❌ 测试图片推送失败")
                return False

        except Exception as e:
            log.error(f"准备测试图片失败: {e}")
            return False


if __name__ == '__main__':
    from pages.apps.ella.dialogue_page import EllaDialoguePage

    ella_page = EllaDialoguePage()
    ella_page.start_app()
    ella_page.wait_for_page_load()
    handler = EllaMultimodalHandler(ella_page.driver, ella_page.page_elements)
    handler.execute_multimodal_function("document")
