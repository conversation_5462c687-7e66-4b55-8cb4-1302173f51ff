"""
Ella多模态功能处理器
处理文档、图片、相机、AI生图等多模态功能的业务流程
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from core.logger import log
from config.multimodal_config import (
    get_timeout, get_retry_count, get_function_config,
    get_camera_shutter_selectors, get_camera_confirm_selectors,
    get_file_confirm_buttons, get_gallery_confirm_buttons,
    should_continue_on_error, is_debug_enabled,
    get_ai_image_scale_options, get_ai_image_scale_selectors
)
from tools.file_pusher import file_pusher


class EllaMultimodalHandler:
    """Ella多模态功能处理器"""

    def __init__(self, driver, page_elements):
        """
        初始化多模态处理器
        
        Args:
            driver: UI自动化驱动器
            page_elements: 页面元素字典
        """
        self.driver = driver
        self.page_elements = page_elements

    def execute_multimodal_function(self, model: str) -> bool:
        """
        根据模型类型执行对应的多模态功能
        
        Args:
            model: 功能模型类型 (document/gallery/camera/ai_image_generator)
            
        Returns:
            bool: 执行是否成功
        """
        try:
            log.info(f"执行多模态功能: {model}")

            # 首先点击多模态入口
            if not self._open_multimodal_entrance():
                return False

            # 根据模型类型执行对应功能
            if model == "document":
                return self._handle_document_flow()
            elif model == "gallery":
                return self._handle_gallery_flow()
            elif model == "camera":
                return self._handle_camera_flow()
            elif model == "ai_image_generator":
                return self._handle_ai_image_generator_flow()
            else:
                log.error(f"不支持的模型类型: {model}")
                return False

        except Exception as e:
            log.error(f"执行多模态功能失败: {e}")
            return False

    def _open_multimodal_entrance(self) -> bool:
        """打开多模态入口"""
        try:
            log.info("点击多模态入口")

            # 获取多模态入口按钮
            entrance_button = self.page_elements.get('multi_modal_entrance')
            if not entrance_button:
                log.error("未找到多模态入口按钮元素")
                return False

            if entrance_button.is_exists():
                entrance_button.click()
                time.sleep(get_timeout("entrance_open"))  # 等待菜单展开
                log.info("✅ 成功点击多模态入口")
                return True
            else:
                log.error("多模态入口按钮不存在")
                return False

        except Exception as e:
            log.error(f"打开多模态入口失败: {e}")
            return False

    def _handle_document_flow(self) -> bool:
        """
        处理文档功能流程
        推送测试文档->点击document按钮->选择文件
        """
        try:
            log.info("执行文档功能流程")

            # 0. 推送测试文档到设备
            if not self._prepare_test_documents():
                log.warning("推送测试文档失败，但继续执行")

            # 1. 点击document按钮
            document_button = self.page_elements.get('multi_modal_document')
            if not document_button or not document_button.is_exists():
                log.error("未找到document按钮")
                return False

            document_button.click()
            time.sleep(get_timeout("button_click"))
            log.info("✅ 点击document按钮成功")

            # 2. 选择文件 (文件选择完成后会自动返回)
            if not self._select_file():
                log.warning("文件选择可能失败")
                return should_continue_on_error("selection_failure")

            log.info("✅ 文档功能流程完成")
            return True

        except Exception as e:
            log.error(f"文档功能流程失败: {e}")
            return False

    def _handle_gallery_flow(self) -> bool:
        """
        处理图库功能流程
        推送测试图片->点击gallery->选中照片
        """
        try:
            log.info("执行图库功能流程")

            # 0. 推送测试图片到设备
            if not self._prepare_test_images():
                log.warning("推送测试图片失败，但继续执行")

            # 1. 点击gallery按钮
            gallery_button = self.page_elements.get('multi_modal_gallery')
            if not gallery_button or not gallery_button.is_exists():
                log.error("未找到gallery按钮")
                return False

            gallery_button.click()
            time.sleep(get_timeout("app_launch"))
            log.info("✅ 点击gallery按钮成功")

            # 2. 选中照片 (照片选择完成后会自动返回)
            if not self._select_photo():
                log.warning("照片选择可能失败")
                return should_continue_on_error("selection_failure")

            log.info("✅ 图库功能流程完成")
            return True

        except Exception as e:
            log.error(f"图库功能流程失败: {e}")
            return False

    def _handle_camera_flow(self) -> bool:
        """
        处理相机功能流程
        点击camera->拍照->确认照片
        """
        try:
            log.info("执行相机功能流程")

            # 1. 点击camera按钮
            camera_button = self.page_elements.get('multi_modal_camera')
            if not camera_button or not camera_button.is_exists():
                log.error("未找到camera按钮")
                return False

            camera_button.click()
            time.sleep(get_timeout("button_click"))
            log.info("✅ 点击camera按钮成功")

            # 2. 等待相机应用启动
            time.sleep(get_timeout("app_launch"))

            # 3. 拍照
            if not self._take_photo():
                log.warning("拍照可能失败")
                return should_continue_on_error("camera_operation")

            # 4. 确认照片 (确认后会自动返回)
            if not self._confirm_photo():
                log.warning("照片确认可能失败")
                return should_continue_on_error("camera_operation")

            log.info("✅ 相机功能流程完成")
            return True

        except Exception as e:
            log.error(f"相机功能流程失败: {e}")
            return False

    def _handle_ai_image_generator_flow(self) -> bool:
        """
        处理AI生图功能流程
        点击ai_image_generator->配置类型和比例
        """
        try:
            log.info("执行AI生图功能流程")

            # 1. 点击ai_image_generator按钮
            ai_generator_button = self.page_elements.get('multi_modal_ai_image_generator')
            if not ai_generator_button or not ai_generator_button.is_exists():
                log.error("未找到ai_image_generator按钮")
                return False

            ai_generator_button.click()
            time.sleep(get_timeout("button_click"))
            log.info("✅ 点击ai_image_generator按钮成功")

            # 2. 配置AI生图类型 (可选)
            type_button = self.page_elements.get('multi_modal_ai_image_generator_type')
            if type_button and type_button.is_exists():
                type_button.click()
                time.sleep(get_timeout("ai_generator_setup"))
                log.info("✅ 配置ai_image_generator_type成功")
            else:
                log.info("ai_image_generator_type按钮不存在，跳过配置")

            # # 3. 配置AI生图比例 (可选)
            # scale_button = self.page_elements.get('multi_modal_ai_image_generator_scale')
            # if scale_button and scale_button.is_exists():
            #     scale_button.click()
            #     time.sleep(get_timeout("ai_generator_setup"))
            #     log.info("✅ 点击ai_image_generator_scale按钮成功")
            # else:
            #     log.info("ai_image_generator_scale按钮不存在，跳过配置")

            # TODO 比例选项，选不到，暂时使用默认1:1比例
            # # 4 处理弹出的比例选择浮窗
            # if self._select_ai_image_scale_option():
            #     log.info("✅ 配置ai_image_generator_enmu成功")
            # else:
            #     log.warning("比例选择可能失败，但继续执行")

            log.info("✅ AI生图功能流程完成")
            return True

        except Exception as e:
            log.error(f"AI生图功能流程失败: {e}")
            return False

    def _select_file(self) -> bool:
        """选择文件"""
        try:
            log.info("尝试选择文件")

            # 等待文件选择器出现
            time.sleep(get_timeout("file_selection"))

            # 方法1: 尝试选择推送的测试文档
            if self._select_specific_file("bcy_doc.txt"):
                return True

            # 方法2: 尝试选择任何文档文件
            if self._select_document_file():
                return True

            # 方法3: 通用文件选择
            if self._select_generic_file():
                return True

            # 方法4: 尝试点击确认按钮（可能已经有默认选择）
            if self._try_confirm_selection():
                return True

            log.warning("所有文件选择方法都失败，但继续执行")
            return True

        except Exception as e:
            log.error(f"选择文件失败: {e}")
            return False

    def _select_specific_file(self, filename: str) -> bool:
        """选择特定文件名的文件"""
        try:
            log.info(f"尝试选择特定文件: {filename}")

            # 查找包含特定文件名的元素
            file_element = self.driver(text=filename)
            if file_element.exists(timeout=3):
                file_element.click()
                time.sleep(get_timeout("button_click"))
                log.info(f"✅ 成功选择文件: {filename}")
                return True
                # # 尝试确认选择
                # return self._try_confirm_selection()

            # 尝试查找包含文件名的TextView
            text_views = self.driver(className="android.widget.TextView")
            if text_views.exists():
                for i in range(min(len(text_views), 10)):  # 最多检查10个元素
                    try:
                        text = text_views[i].get_text()
                        if filename in text:
                            text_views[i].click()
                            time.sleep(get_timeout("button_click"))
                            log.info(f"✅ 通过TextView选择文件: {filename}")
                            return self._try_confirm_selection()
                    except:
                        continue

            return False

        except Exception as e:
            log.error(f"选择特定文件失败: {e}")
            return False

    def _select_document_file(self) -> bool:
        """选择任何文档文件"""
        try:
            log.info("尝试选择任何文档文件")

            # 文档文件扩展名
            doc_extensions = ['.txt', '.pdf', '.doc', '.docx', '.rtf']

            # 查找包含文档扩展名的TextView
            text_views = self.driver(className="android.widget.TextView")
            if text_views.exists():
                for i in range(min(len(text_views), 15)):  # 最多检查15个元素
                    try:
                        text = text_views[i].get_text()
                        if any(ext in text.lower() for ext in doc_extensions):
                            text_views[i].click()
                            time.sleep(get_timeout("button_click"))
                            log.info(f"✅ 选择文档文件: {text}")
                            return self._try_confirm_selection()
                    except:
                        continue

            return False

        except Exception as e:
            log.error(f"选择文档文件失败: {e}")
            return False

    def _select_generic_file(self) -> bool:
        """通用文件选择"""
        try:
            log.info("尝试通用文件选择")

            # 方法1: 尝试点击RecyclerView中的第一个项目
            recycler_items = self.driver(className="androidx.recyclerview.widget.RecyclerView").child(
                className="android.view.ViewGroup")
            if recycler_items.exists():
                if len(recycler_items) > 0:
                    recycler_items[0].click()
                    time.sleep(get_timeout("button_click"))
                    log.info("✅ 通过RecyclerView选择文件")
                    return self._try_confirm_selection()

            # 方法2: 尝试点击LinearLayout中的项目
            linear_items = self.driver(className="android.widget.LinearLayout")
            if linear_items.exists():
                for i in range(min(len(linear_items), 5)):
                    try:
                        # 检查是否包含文件相关的子元素
                        if linear_items[i].child(className="android.widget.TextView").exists():
                            linear_items[i].click()
                            time.sleep(get_timeout("button_click"))
                            log.info(f"✅ 通过LinearLayout选择文件 (第{i + 1}个)")
                            return self._try_confirm_selection()
                    except:
                        continue

            # 方法3: 尝试点击任何可点击的TextView
            clickable_texts = self.driver(className="android.widget.TextView", clickable=True)
            if clickable_texts.exists():
                if len(clickable_texts) > 0:
                    clickable_texts[0].click()
                    time.sleep(get_timeout("button_click"))
                    log.info("✅ 通过可点击TextView选择文件")
                    return self._try_confirm_selection()

            return False

        except Exception as e:
            log.error(f"通用文件选择失败: {e}")
            return False

    def _try_confirm_selection(self) -> bool:
        """尝试确认文件选择"""
        try:
            log.info("尝试确认文件选择")

            # 等待一下，让界面响应
            time.sleep(1)

            # 尝试点击确认按钮
            confirm_buttons = get_file_confirm_buttons()
            for btn_text in confirm_buttons:
                btn = self.driver(text=btn_text)
                if btn.exists(timeout=2):
                    btn.click()
                    time.sleep(get_timeout("button_click"))
                    log.info(f"✅ 点击{btn_text}按钮确认文件选择")
                    return True

            # 尝试查找其他可能的确认按钮
            confirm_selectors = [
                {"resourceId": "android:id/button1"},  # 对话框确认按钮
                {"resourceId": "com.android.documentsui:id/action_menu_select"},
                {"description": "确认"},
                {"description": "Select"},
                {"className": "android.widget.Button", "text": "确定"},
                {"className": "android.widget.Button", "text": "OK"}
            ]

            for selector in confirm_selectors:
                btn = self.driver(**selector)
                if btn.exists(timeout=1):
                    btn.click()
                    time.sleep(get_timeout("button_click"))
                    log.info(f"✅ 通过选择器确认文件选择: {selector}")
                    return True

            # 如果没有找到确认按钮，可能文件已经被选择
            log.info("未找到确认按钮，文件可能已自动选择")
            return True

        except Exception as e:
            log.error(f"确认文件选择失败: {e}")
            return False

    def _select_photo(self) -> bool:
        """选择照片"""
        try:
            log.info("尝试选择照片")

            # 等待图库界面加载
            time.sleep(get_timeout("photo_selection"))

            # # 方法1: 尝试选择推送的测试图片
            # if self._select_specific_image():
            #     return True

            # 方法2: 尝试选择任何图片
            if self._select_any_image():
                return True

            # 方法3: 尝试通过RecyclerView选择
            if self._select_image_from_recyclerview():
                return True

            # 方法4: 尝试确认默认选择
            if self._try_confirm_photo_selection():
                return True

            log.warning("所有照片选择方法都失败，但继续执行")
            return True

        except Exception as e:
            log.error(f"选择照片失败: {e}")
            return False

    def _select_specific_image(self) -> bool:
        """选择特定的测试图片"""
        try:
            log.info("尝试选择特定测试图片")

            # 查找推送的测试图片
            test_images = ["88.jpg", "89.jpg"]

            for image_name in test_images:
                # 尝试通过描述或内容描述查找
                image_element = self.driver(description=image_name)
                if image_element.exists(timeout=2):
                    image_element.click()
                    time.sleep(get_timeout("button_click"))
                    log.info(f"✅ 选择特定图片: {image_name}")
                    return self._try_confirm_photo_selection()

            return False

        except Exception as e:
            log.error(f"选择特定图片失败: {e}")
            return False

    def _select_any_image(self) -> bool:
        """选择任何可用的图片"""
        try:
            log.info("尝试选择任何可用图片")

            # 方法1: 查找ImageView
            images = self.driver(className="android.widget.ImageView")
            if images.exists(timeout=get_timeout("photo_selection")):
                # 过滤掉可能的UI元素，选择较大的ImageView（通常是照片）
                for i in range(min(len(images), 10)):
                    try:
                        image = images[i]
                        # 检查ImageView的大小，通常照片会比较大
                        bounds = image.info.get('bounds', {})
                        if bounds:
                            width = bounds.get('right', 0) - bounds.get('left', 0)
                            height = bounds.get('bottom', 0) - bounds.get('top', 0)

                            # 如果ImageView足够大，可能是照片
                            if width > 100 and height > 100:
                                image.click()
                                time.sleep(get_timeout("button_click"))
                                log.info(f"✅ 选择图片 (第{i + 1}个ImageView)")
                                return self._try_confirm_photo_selection()
                    except:
                        continue

                # 如果上面的方法失败，直接选择第一个ImageView
                if len(images) > 0:
                    images[0].click()
                    time.sleep(get_timeout("button_click"))
                    log.info("✅ 选择第一个ImageView")
                    return self._try_confirm_photo_selection()

            return False

        except Exception as e:
            log.error(f"选择任何图片失败: {e}")
            return False

    def _select_image_from_recyclerview(self) -> bool:
        """从RecyclerView中选择图片"""
        try:
            log.info("尝试从RecyclerView选择图片")

            # 查找RecyclerView
            recycler_view = self.driver(className="androidx.recyclerview.widget.RecyclerView")
            if recycler_view.exists():
                # 获取RecyclerView中的子项
                items = recycler_view.child()
                if items.exists():
                    # 选择第一个项目
                    if len(items) > 0:
                        items[0].click()
                        time.sleep(get_timeout("button_click"))
                        log.info("✅ 从RecyclerView选择图片")
                        return self._try_confirm_photo_selection()

            return False

        except Exception as e:
            log.error(f"从RecyclerView选择图片失败: {e}")
            return False

    def _try_confirm_photo_selection(self) -> bool:
        """尝试确认照片选择"""
        try:
            log.info("尝试确认照片选择")

            # 等待界面响应
            time.sleep(1)

            # 尝试点击确认按钮
            confirm_buttons = get_gallery_confirm_buttons()
            for btn_text in confirm_buttons:
                btn = self.driver(text=btn_text)
                if btn.exists(timeout=2):
                    btn.click()
                    time.sleep(get_timeout("button_click"))
                    log.info(f"✅ 点击{btn_text}按钮确认照片选择")
                    return True

            # 尝试其他可能的确认按钮
            confirm_selectors = [
                {"resourceId": "android:id/button1"},
                {"resourceId": "com.android.gallery3d:id/action_confirm"},
                {"resourceId": "com.gallery20:id/action_confirm"},
                {"description": "确认"},
                {"description": "Select"},
                {"className": "android.widget.Button", "text": "确定"},
                {"className": "android.widget.Button", "text": "OK"}
            ]

            for selector in confirm_selectors:
                btn = self.driver(**selector)
                if btn.exists(timeout=1):
                    btn.click()
                    time.sleep(get_timeout("button_click"))
                    log.info(f"✅ 通过选择器确认照片选择: {selector}")
                    return True

            # 如果没有找到确认按钮，可能照片已经被选择
            log.info("未找到确认按钮，照片可能已自动选择")
            return True

        except Exception as e:
            log.error(f"确认照片选择失败: {e}")
            return False

    def _take_photo(self) -> bool:
        """拍照"""
        try:
            log.info("尝试拍照")

            # 等待相机界面加载
            time.sleep(get_timeout("camera_ready"))

            # 尝试找到拍照按钮并点击
            shutter_selectors = get_camera_shutter_selectors()

            for selector in shutter_selectors:
                shutter_btn = self.driver(**selector)
                if shutter_btn.exists(timeout=get_timeout("camera_ready")):
                    shutter_btn.click()
                    time.sleep(get_timeout("photo_capture"))
                    log.info("✅ 拍照成功")
                    return True

            # 如果找不到特定按钮，尝试点击屏幕下方中央（通常是拍照按钮位置）
            try:
                width, height = self.driver.window_size()
                self.driver.click(width // 2, int(height * 0.85))
                time.sleep(get_timeout("photo_capture"))
                log.info("✅ 通过坐标点击拍照成功")
                return True
            except Exception as e:
                log.warning(f"坐标点击拍照失败: {e}")
                return False

        except Exception as e:
            log.error(f"拍照失败: {e}")
            return False

    def _confirm_photo(self) -> bool:
        """确认照片"""
        try:
            log.info("尝试确认照片")

            # 等待照片预览界面
            time.sleep(get_timeout("photo_confirm"))

            # 尝试找到确认按钮
            confirm_selectors = get_camera_confirm_selectors()

            for selector in confirm_selectors:
                confirm_btn = self.driver(**selector)
                if confirm_btn.exists(timeout=get_timeout("photo_confirm")):
                    confirm_btn.click()
                    time.sleep(get_timeout("button_click"))
                    log.info("✅ 确认照片成功")
                    return True

            # 如果没有找到确认按钮，可能照片已自动保存
            log.info("未找到确认按钮，照片可能已自动保存")
            return True

        except Exception as e:
            log.error(f"确认照片失败: {e}")
            return False

    def _prepare_test_documents(self) -> bool:
        """准备测试文档"""
        try:
            log.info("开始推送测试文档到设备")

            # 检查设备连接
            if not file_pusher.check_device_connection():
                log.error("设备未连接，无法推送测试文档")
                return False

            # 推送文档到Download目录
            target_path = "/sdcard/Download"
            if file_pusher.push_documents_to_device(target_path):
                log.info("✅ 测试文档推送成功")

                # 等待文件系统同步
                time.sleep(get_timeout("button_click"))

                # 验证文件是否推送成功
                files = file_pusher.list_device_files(target_path)
                if files:
                    log.info(f"设备中的文件: {files}")

                return True
            else:
                log.error("❌ 测试文档推送失败")
                return False

        except Exception as e:
            log.error(f"准备测试文档失败: {e}")
            return False

    def _prepare_test_images(self) -> bool:
        """准备测试图片"""
        try:
            log.info("开始推送测试图片到设备")

            # 检查设备连接
            if not file_pusher.check_device_connection():
                log.error("设备未连接，无法推送测试图片")
                return False

            # 推送图片到Pictures目录
            target_path = "/sdcard/Download"
            if file_pusher.push_images_to_device(target_path):
                log.info("✅ 测试图片推送成功")

                # 等待文件系统同步
                time.sleep(get_timeout("button_click"))

                # 验证文件是否推送成功
                files = file_pusher.list_device_files(target_path)
                if files:
                    log.info(f"设备中的图片: {files}")

                return True
            else:
                log.error("❌ 测试图片推送失败")
                return False

        except Exception as e:
            log.error(f"准备测试图片失败: {e}")
            return False

    def _select_ai_image_scale_option(self) -> bool:
        """选择AI生图比例选项"""
        try:
            log.info("尝试选择AI生图比例选项")

            # 等待浮窗出现
            time.sleep(get_timeout("ai_generator_setup"))

            # 方法1: 通过multi_modal_ai_image_generator_scale_enum元素选择
            if self._select_from_scale_enum_list():
                return True

            # 方法2: 查找android.view.View类型的浮窗选项
            if self._select_from_scale_popup():
                return True

            # 方法3: 通过文本选择常见的比例选项
            if self._select_scale_by_text():
                return True

            # 方法4: 选择任何可点击的选项
            if self._select_any_scale_option():
                return True

            log.warning("所有比例选择方法都失败")
            return False

        except Exception as e:
            log.error(f"选择AI生图比例选项失败: {e}")
            return False

    def _select_from_scale_enum_list(self) -> bool:
        """从比例枚举列表中选择选项"""
        try:
            log.info("尝试从比例枚举列表中选择")

            # 获取比例枚举元素（这是一个列表）
            scale_enum_element = self.page_elements.get('multi_modal_ai_image_generator_scale_enum')
            if not scale_enum_element:
                log.warning("未找到scale_enum元素")
                return False

            # 方法1: 直接点击枚举元素（如果它是单个元素）
            if scale_enum_element.is_exists():
                try:
                    scale_enum_element.click()
                    time.sleep(get_timeout("button_click"))
                    log.info("✅ 直接点击scale_enum元素成功")
                    return True
                except Exception as e:
                    log.debug(f"直接点击scale_enum失败: {e}")

            # 方法2: 查找所有android.view.View元素（比例选项列表）
            scale_views = self.driver(className="android.view.View")
            if scale_views.exists(timeout=3):
                log.info(f"找到 {len(scale_views)} 个View元素")

                # 优先选择包含常见比例的元素
                preferred_scales = ["1:1", "16:9", "9:16", "4:3", "3:4"]

                for scale in preferred_scales:
                    for i in range(min(len(scale_views), 15)):  # 最多检查15个元素
                        try:
                            view = scale_views[i]
                            # 检查元素的文本内容或描述
                            content_desc = view.info.get('contentDescription', '')
                            if scale in content_desc:
                                view.click()
                                time.sleep(get_timeout("button_click"))
                                log.info(f"✅ 通过描述选择比例成功: {scale}")
                                return True
                        except:
                            continue

                # 如果没有找到特定比例，选择第一个可点击的View
                for i in range(min(len(scale_views), 10)):
                    try:
                        view = scale_views[i]
                        if view.info.get('clickable', True):  # 默认认为是可点击的
                            view.click()
                            time.sleep(get_timeout("button_click"))
                            log.info(f"✅ 选择第一个可点击的比例选项 (第{i + 1}个)")
                            return True
                    except Exception as e:
                        log.debug(f"点击第{i + 1}个View失败: {e}")
                        continue

            return False

        except Exception as e:
            log.error(f"从比例枚举列表中选择失败: {e}")
            return False

    def _select_from_scale_popup(self) -> bool:
        """从比例选择浮窗中选择选项"""
        try:
            log.info("尝试从比例选择浮窗中选择")

            # 查找android.view.View类型的浮窗元素
            popup_views = self.driver(className="android.view.View")
            if popup_views.exists(timeout=3):
                log.info(f"找到 {len(popup_views)} 个浮窗View元素")

                # 方法1: 查找包含比例文本的View元素
                scale_texts = get_ai_image_scale_options()
                for scale_text in scale_texts:
                    for i in range(min(len(popup_views), 15)):
                        try:
                            view = popup_views[i]
                            # 检查内容描述或其他属性
                            content_desc = view.info.get('contentDescription', '')
                            resource_id = view.info.get('resourceName', '')

                            if scale_text in content_desc or scale_text in resource_id:
                                view.click()
                                time.sleep(get_timeout("button_click"))
                                log.info(f"✅ 通过文本匹配选择比例: {scale_text}")
                                return True
                        except:
                            continue

                # 方法2: 选择有合适尺寸的View元素（过滤掉太小的装饰性元素）
                for i in range(min(len(popup_views), 15)):
                    try:
                        view = popup_views[i]
                        bounds = view.info.get('bounds', {})

                        if bounds:
                            width = bounds.get('right', 0) - bounds.get('left', 0)
                            height = bounds.get('bottom', 0) - bounds.get('top', 0)

                            # 选择有一定尺寸的元素（可能是比例选项）
                            if width > 50 and height > 30:
                                view.click()
                                time.sleep(get_timeout("button_click"))
                                log.info(f"✅ 通过尺寸筛选选择比例 (第{i + 1}个, {width}x{height})")
                                return True
                    except Exception as e:
                        log.debug(f"尺寸筛选第{i + 1}个元素失败: {e}")
                        continue

                # 方法3: 选择第一个View元素作为备选
                if len(popup_views) > 0:
                    try:
                        popup_views[0].click()
                        time.sleep(get_timeout("button_click"))
                        log.info("✅ 选择第一个浮窗View元素")
                        return True
                    except Exception as e:
                        log.debug(f"选择第一个View元素失败: {e}")

            return False

        except Exception as e:
            log.error(f"从比例选择浮窗中选择失败: {e}")
            return False

    def _select_scale_by_text(self) -> bool:
        """通过文本选择比例选项"""
        try:
            log.info("尝试通过文本选择比例选项")

            # 从配置文件获取比例选项文本
            scale_options = get_ai_image_scale_options()

            for option_text in scale_options:
                option_element = self.driver(text=option_text)
                if option_element.exists(timeout=2):
                    option_element.click()
                    time.sleep(get_timeout("button_click"))
                    log.info(f"✅ 通过文本选择比例成功: {option_text}")
                    return True

            return False

        except Exception as e:
            log.error(f"通过文本选择比例失败: {e}")
            return False

    def _select_any_scale_option(self) -> bool:
        """选择任何可用的比例选项"""
        try:
            log.info("尝试选择任何可用的比例选项")

            # 方法1: 使用配置文件中的选择器
            scale_selectors = get_ai_image_scale_selectors()
            for selector in scale_selectors:
                elements = self.driver(**selector)
                if elements.exists(timeout=2):
                    if len(elements) > 0:
                        elements[0].click()
                        time.sleep(get_timeout("button_click"))
                        log.info(f"✅ 通过选择器选择比例成功: {selector}")
                        return True

            # 方法2: 查找可点击的TextView（可能包含比例文本）
            clickable_texts = self.driver(className="android.widget.TextView", clickable=True)
            if clickable_texts.exists(timeout=2):
                for i in range(min(len(clickable_texts), 5)):  # 最多尝试5个
                    try:
                        text = clickable_texts[i].get_text()
                        # 检查是否包含比例相关的文本
                        if any(char in text for char in [":", "×", "x", "1", "9", "16", "4", "3"]):
                            clickable_texts[i].click()
                            time.sleep(get_timeout("button_click"))
                            log.info(f"✅ 通过TextView选择比例成功: {text}")
                            return True
                    except:
                        continue

            # 方法3: 选择第一个可点击的元素（作为最后的尝试）
            clickable_elements = self.driver(clickable=True)
            if clickable_elements.exists(timeout=2):
                if len(clickable_elements) > 0:
                    clickable_elements[0].click()
                    time.sleep(get_timeout("button_click"))
                    log.info("✅ 选择第一个可点击元素作为比例选项")
                    return True

            return False

        except Exception as e:
            log.error(f"选择任何比例选项失败: {e}")
            return False


if __name__ == '__main__':
    from pages.apps.ella.dialogue_page import EllaDialoguePage

    ella_page = EllaDialoguePage()
    ella_page.start_app()
    ella_page.wait_for_page_load()
    handler = EllaMultimodalHandler(ella_page.driver, ella_page.page_elements)
    # handler.execute_multimodal_function("document")
    # handler.execute_multimodal_function("gallery")
    # handler.execute_multimodal_function("camera")
    handler.execute_multimodal_function("ai_image_generator")
