"""
测试AI生图比例列表选择功能
验证从比例选项列表中选择的逻辑
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log


def test_scale_enum_list_analysis():
    """分析比例枚举列表结构"""
    print("\n🔍 比例枚举列表分析")
    print("="*50)
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            print("❌ 启动失败")
            return False
        
        print("请手动导航到AI生图比例选择页面，点击比例按钮后按回车键开始分析...")
        input()
        
        # 分析比例枚举元素
        scale_enum_element = ella_page.page_elements.get('multi_modal_ai_image_generator_scale_enum')
        if scale_enum_element:
            print("✅ 找到scale_enum元素")
            if scale_enum_element.is_exists():
                print("✅ scale_enum元素存在")
                try:
                    info = scale_enum_element.info
                    print(f"   元素信息: {info}")
                except Exception as e:
                    print(f"   获取元素信息失败: {e}")
            else:
                print("❌ scale_enum元素不存在")
        else:
            print("❌ 未找到scale_enum元素")
        
        # 分析所有android.view.View元素
        print("\n分析android.view.View元素...")
        views = ella_page.driver(className="android.view.View")
        if views.exists():
            print(f"找到 {len(views)} 个android.view.View元素")
            
            for i in range(min(len(views), 20)):  # 分析前20个
                try:
                    view = views[i]
                    bounds = view.info.get('bounds', {})
                    width = bounds.get('right', 0) - bounds.get('left', 0)
                    height = bounds.get('bottom', 0) - bounds.get('top', 0)
                    clickable = view.info.get('clickable', False)
                    content_desc = view.info.get('contentDescription', '')
                    resource_id = view.info.get('resourceName', '')
                    
                    print(f"  View {i+1}:")
                    print(f"    尺寸: {width}x{height}")
                    print(f"    可点击: {clickable}")
                    print(f"    描述: '{content_desc}'")
                    print(f"    资源ID: '{resource_id}'")
                    
                    # 检查是否包含比例相关信息
                    scale_indicators = ["1:1", "16:9", "9:16", "4:3", "3:4", "比例", "scale", "ratio"]
                    has_scale_info = any(indicator in content_desc.lower() or indicator in resource_id.lower() 
                                       for indicator in scale_indicators)
                    if has_scale_info:
                        print(f"    ⭐ 可能是比例选项")
                    print()
                    
                except Exception as e:
                    print(f"  View {i+1}: 分析失败 - {e}")
        else:
            print("未找到android.view.View元素")
        
        # 分析其他可能的比例选项元素
        print("\n分析其他可能的比例选项...")
        
        # TextView元素
        text_views = ella_page.driver(className="android.widget.TextView")
        if text_views.exists():
            print(f"找到 {len(text_views)} 个TextView元素")
            for i in range(min(len(text_views), 10)):
                try:
                    text = text_views[i].get_text()
                    if text and any(char in text for char in [":", "×", "x", "1", "9", "16", "4", "3"]):
                        print(f"  可能的比例TextView: '{text}'")
                except:
                    continue
        
        # 可点击元素
        clickable_elements = ella_page.driver(clickable=True)
        if clickable_elements.exists():
            print(f"\n找到 {len(clickable_elements)} 个可点击元素")
            scale_related = 0
            for i in range(min(len(clickable_elements), 15)):
                try:
                    element = clickable_elements[i]
                    class_name = element.info.get('className', '')
                    content_desc = element.info.get('contentDescription', '')
                    
                    # 检查是否与比例相关
                    if any(indicator in content_desc.lower() for indicator in ["1:1", "16:9", "9:16", "4:3", "3:4", "比例", "scale"]):
                        scale_related += 1
                        print(f"  比例相关元素 {scale_related}: {class_name}, 描述: '{content_desc}'")
                except:
                    continue
        
        return True
        
    except Exception as e:
        print(f"分析失败: {e}")
        return False


def test_scale_list_selection_methods():
    """测试不同的比例列表选择方法"""
    print("\n🧪 测试比例列表选择方法")
    print("="*50)
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            print("❌ 启动失败")
            return False
        
        print("请手动导航到AI生图比例选择页面，点击比例按钮后按回车键开始测试...")
        input()
        
        # 测试方法1: 从比例枚举列表选择
        print("\n1. 测试从比例枚举列表选择...")
        result1 = ella_page.multimodal_handler._select_from_scale_enum_list()
        print(f"   结果: {'✅ 成功' if result1 else '❌ 失败'}")
        
        if not result1:
            print("\n2. 测试从浮窗选择...")
            result2 = ella_page.multimodal_handler._select_from_scale_popup()
            print(f"   结果: {'✅ 成功' if result2 else '❌ 失败'}")
            
            if not result2:
                print("\n3. 测试通过文本选择...")
                result3 = ella_page.multimodal_handler._select_scale_by_text()
                print(f"   结果: {'✅ 成功' if result3 else '❌ 失败'}")
                
                if not result3:
                    print("\n4. 测试选择任何可用选项...")
                    result4 = ella_page.multimodal_handler._select_any_scale_option()
                    print(f"   结果: {'✅ 成功' if result4 else '❌ 失败'}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_complete_ai_image_flow():
    """测试完整的AI生图流程"""
    print("\n🎨 测试完整AI生图流程")
    print("="*50)
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            print("❌ 启动失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load():
            print("❌ 页面加载失败")
            return False
        
        # 确保在对话页面
        if not ella_page.ensure_on_chat_page():
            print("❌ 无法确保在对话页面")
            return False
        
        print("开始执行完整AI生图流程...")
        
        # 执行AI生图功能
        result = ella_page.execute_multimodal_function("ai_image_generator")
        
        print(f"完整流程结果: {'✅ 成功' if result else '❌ 失败'}")
        return result
        
    except Exception as e:
        print(f"完整流程测试失败: {e}")
        return False


def interactive_scale_test():
    """交互式比例选择测试"""
    print("\n🔧 交互式比例选择测试")
    print("="*50)
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            print("❌ 启动失败")
            return False
        
        while True:
            print("\n请选择测试功能:")
            print("1. 分析比例列表结构")
            print("2. 测试选择方法")
            print("3. 测试完整流程")
            print("4. 手动测试单个方法")
            print("5. 退出")
            
            choice = input("请输入选择 (1-5): ").strip()
            
            if choice == "1":
                test_scale_enum_list_analysis()
                
            elif choice == "2":
                test_scale_list_selection_methods()
                
            elif choice == "3":
                test_complete_ai_image_flow()
                
            elif choice == "4":
                print("\n手动测试:")
                print("请导航到比例选择页面，然后选择要测试的方法:")
                print("1. 枚举列表选择")
                print("2. 浮窗选择")
                print("3. 文本选择")
                print("4. 任意选项选择")
                
                method_choice = input("请输入方法选择 (1-4): ").strip()
                input("准备好后按回车键...")
                
                if method_choice == "1":
                    result = ella_page.multimodal_handler._select_from_scale_enum_list()
                elif method_choice == "2":
                    result = ella_page.multimodal_handler._select_from_scale_popup()
                elif method_choice == "3":
                    result = ella_page.multimodal_handler._select_scale_by_text()
                elif method_choice == "4":
                    result = ella_page.multimodal_handler._select_any_scale_option()
                else:
                    print("无效选择")
                    continue
                
                print(f"测试结果: {'✅ 成功' if result else '❌ 失败'}")
                
            elif choice == "5":
                print("退出测试")
                break
                
            else:
                print("无效选择，请重新输入")
        
        return True
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
        return False
    except Exception as e:
        print(f"交互式测试异常: {e}")
        return False


def main():
    """主函数"""
    print("📋 AI生图比例列表选择测试工具")
    print("="*50)
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 分析比例列表结构")
    print("2. 测试选择方法")
    print("3. 测试完整流程")
    print("4. 交互式测试")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    try:
        if choice == "1":
            test_scale_enum_list_analysis()
            
        elif choice == "2":
            test_scale_list_selection_methods()
            
        elif choice == "3":
            test_complete_ai_image_flow()
            
        elif choice == "4":
            interactive_scale_test()
            
        else:
            print("无效选择")
    
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        log.error(f"测试异常: {e}")


if __name__ == "__main__":
    main()
