"""
测试文件选择修复
验证优化后的文件选择逻辑是否有效
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log


def test_document_selection():
    """测试文档选择功能"""
    log.info("=== 测试文档选择功能 ===")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            log.error("启动Ella应用失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 确保在对话页面
        if not ella_page.ensure_on_chat_page():
            log.error("无法确保在对话页面")
            return False
        
        log.info("开始执行文档选择功能...")
        
        # 执行文档功能（包含文件推送和选择）
        result = ella_page.execute_multimodal_function("document")
        
        if result:
            log.info("✅ 文档选择功能测试成功")
        else:
            log.error("❌ 文档选择功能测试失败")
        
        return result
        
    except Exception as e:
        log.error(f"文档选择功能测试异常: {e}")
        return False


def test_gallery_selection():
    """测试图库选择功能"""
    log.info("=== 测试图库选择功能 ===")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            log.error("启动Ella应用失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 确保在对话页面
        if not ella_page.ensure_on_chat_page():
            log.error("无法确保在对话页面")
            return False
        
        log.info("开始执行图库选择功能...")
        
        # 执行图库功能（包含图片推送和选择）
        result = ella_page.execute_multimodal_function("gallery")
        
        if result:
            log.info("✅ 图库选择功能测试成功")
        else:
            log.error("❌ 图库选择功能测试失败")
        
        return result
        
    except Exception as e:
        log.error(f"图库选择功能测试异常: {e}")
        return False


def test_multimodal_entrance():
    """测试多模态入口"""
    log.info("=== 测试多模态入口 ===")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            log.error("启动Ella应用失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 确保在对话页面
        if not ella_page.ensure_on_chat_page():
            log.error("无法确保在对话页面")
            return False
        
        log.info("测试多模态入口点击...")
        
        # 测试多模态入口
        result = ella_page.multimodal_handler._open_multimodal_entrance()
        
        if result:
            log.info("✅ 多模态入口测试成功")
            
            # 等待一下，然后按返回键
            time.sleep(2)
            ella_page.driver.press("back")
        else:
            log.error("❌ 多模态入口测试失败")
        
        return result
        
    except Exception as e:
        log.error(f"多模态入口测试异常: {e}")
        return False


def interactive_test():
    """交互式测试"""
    print("\n🔍 交互式文件选择测试")
    print("="*50)
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        print("1. 启动Ella应用...")
        if not ella_page.start_app():
            print("❌ 启动失败")
            return False
        print("✅ 启动成功")
        
        # 等待页面加载
        print("2. 等待页面加载...")
        if not ella_page.wait_for_page_load():
            print("❌ 页面加载失败")
            return False
        print("✅ 页面加载成功")
        
        while True:
            print("\n请选择测试功能:")
            print("1. 测试文档选择")
            print("2. 测试图库选择")
            print("3. 测试多模态入口")
            print("4. 退出")
            
            choice = input("请输入选择 (1-4): ").strip()
            
            if choice == "1":
                print("\n开始测试文档选择...")
                result = ella_page.execute_multimodal_function("document")
                print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
                
            elif choice == "2":
                print("\n开始测试图库选择...")
                result = ella_page.execute_multimodal_function("gallery")
                print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
                
            elif choice == "3":
                print("\n开始测试多模态入口...")
                result = ella_page.multimodal_handler._open_multimodal_entrance()
                print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
                if result:
                    time.sleep(2)
                    ella_page.driver.press("back")
                
            elif choice == "4":
                print("退出测试")
                break
                
            else:
                print("无效选择，请重新输入")
        
        return True
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
        return False
    except Exception as e:
        print(f"交互式测试异常: {e}")
        return False


def main():
    """主函数"""
    print("🧪 文件选择修复测试工具")
    print("="*50)
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 自动测试所有功能")
    print("2. 交互式测试")
    print("3. 仅测试文档选择")
    print("4. 仅测试图库选择")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    try:
        if choice == "1":
            # 自动测试所有功能
            print("\n开始自动测试...")
            
            results = {
                "多模态入口": test_multimodal_entrance(),
                "文档选择": test_document_selection(),
                "图库选择": test_gallery_selection()
            }
            
            print("\n📊 测试结果汇总:")
            print("="*30)
            for func_name, result in results.items():
                status = "✅ 成功" if result else "❌ 失败"
                print(f"{func_name}: {status}")
            
            success_count = sum(1 for result in results.values() if result)
            total_count = len(results)
            print(f"\n总体结果: {success_count}/{total_count} 个功能测试成功")
            
        elif choice == "2":
            # 交互式测试
            interactive_test()
            
        elif choice == "3":
            # 仅测试文档选择
            result = test_document_selection()
            print(f"\n文档选择测试结果: {'✅ 成功' if result else '❌ 失败'}")
            
        elif choice == "4":
            # 仅测试图库选择
            result = test_gallery_selection()
            print(f"\n图库选择测试结果: {'✅ 成功' if result else '❌ 失败'}")
            
        else:
            print("无效选择")
    
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        log.error(f"测试异常: {e}")


if __name__ == "__main__":
    main()
