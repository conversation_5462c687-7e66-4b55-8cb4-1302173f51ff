"""
Ella多模态功能使用示例
演示如何使用不同的多模态功能

优化后的逻辑特点：
- 执行多模态动作后，无需手动返回Ella页面
- 系统会自动处理文件/照片选择完成后的返回
- 更加简洁高效的流程控制
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log


def test_document_function():
    """测试文档功能"""
    log.info("=== 测试文档功能 ===")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            log.error("启动Ella应用失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 执行文档功能
        result = ella_page.execute_multimodal_function("document")
        
        if result:
            log.info("✅ 文档功能测试成功")
        else:
            log.error("❌ 文档功能测试失败")
        
        return result
        
    except Exception as e:
        log.error(f"文档功能测试异常: {e}")
        return False


def test_gallery_function():
    """测试图库功能"""
    log.info("=== 测试图库功能 ===")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            log.error("启动Ella应用失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 执行图库功能
        result = ella_page.execute_multimodal_function("gallery")
        
        if result:
            log.info("✅ 图库功能测试成功")
        else:
            log.error("❌ 图库功能测试失败")
        
        return result
        
    except Exception as e:
        log.error(f"图库功能测试异常: {e}")
        return False


def test_camera_function():
    """测试相机功能"""
    log.info("=== 测试相机功能 ===")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            log.error("启动Ella应用失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 执行相机功能
        result = ella_page.execute_multimodal_function("camera")
        
        if result:
            log.info("✅ 相机功能测试成功")
        else:
            log.error("❌ 相机功能测试失败")
        
        return result
        
    except Exception as e:
        log.error(f"相机功能测试异常: {e}")
        return False


def test_ai_image_generator_function():
    """测试AI生图功能"""
    log.info("=== 测试AI生图功能 ===")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            log.error("启动Ella应用失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 执行AI生图功能
        result = ella_page.execute_multimodal_function("ai_image_generator")
        
        if result:
            log.info("✅ AI生图功能测试成功")
        else:
            log.error("❌ AI生图功能测试失败")
        
        return result
        
    except Exception as e:
        log.error(f"AI生图功能测试异常: {e}")
        return False


def test_all_multimodal_functions():
    """测试所有多模态功能"""
    log.info("=== 开始测试所有多模态功能 ===")
    
    functions = [
        ("document", test_document_function),
        ("gallery", test_gallery_function),
        ("camera", test_camera_function),
        ("ai_image_generator", test_ai_image_generator_function)
    ]
    
    results = {}
    
    for func_name, test_func in functions:
        log.info(f"\n开始测试 {func_name} 功能...")
        try:
            result = test_func()
            results[func_name] = result
            time.sleep(3)  # 每个测试之间间隔3秒
        except Exception as e:
            log.error(f"测试 {func_name} 功能时发生异常: {e}")
            results[func_name] = False
    
    # 输出测试结果汇总
    log.info("\n=== 测试结果汇总 ===")
    for func_name, result in results.items():
        status = "✅ 成功" if result else "❌ 失败"
        log.info(f"{func_name}: {status}")
    
    success_count = sum(1 for result in results.values() if result)
    total_count = len(results)
    log.info(f"\n总体结果: {success_count}/{total_count} 个功能测试成功")
    
    return results


if __name__ == "__main__":
    # 可以单独测试某个功能
    import argparse
    
    parser = argparse.ArgumentParser(description="Ella多模态功能测试")
    parser.add_argument("--function", choices=["document", "gallery", "camera", "ai_image_generator", "all"], 
                       default="all", help="要测试的功能")
    
    args = parser.parse_args()
    
    if args.function == "document":
        test_document_function()
    elif args.function == "gallery":
        test_gallery_function()
    elif args.function == "camera":
        test_camera_function()
    elif args.function == "ai_image_generator":
        test_ai_image_generator_function()
    else:
        test_all_multimodal_functions()
