"""
测试导入修复
验证所有导入是否正常工作
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """测试所有相关的导入"""
    print("🔍 测试导入修复...")
    
    try:
        # 测试核心模块导入
        print("1. 测试核心模块导入...")
        from core.logger import log
        print("   ✅ core.logger 导入成功")
        
        from core.base_driver import driver_manager
        print("   ✅ core.base_driver.driver_manager 导入成功")
        
        # 测试页面模块导入
        print("2. 测试页面模块导入...")
        from pages.apps.ella.dialogue_page import EllaDialoguePage
        print("   ✅ EllaDialoguePage 导入成功")
        
        # 测试工具模块导入
        print("3. 测试工具模块导入...")
        from tools.file_pusher import FilePusher
        print("   ✅ FilePusher 导入成功")
        
        from tools.file_selector_debugger import FileSelectorDebugger
        print("   ✅ FileSelectorDebugger 导入成功")
        
        # 测试配置模块导入
        print("4. 测试配置模块导入...")
        from config.multimodal_config import get_timeout, get_file_confirm_buttons
        print("   ✅ multimodal_config 导入成功")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能...")
    
    try:
        # 测试日志功能
        from core.logger import log
        log.info("测试日志功能")
        print("   ✅ 日志功能正常")
        
        # 测试驱动管理器
        from core.base_driver import driver_manager
        device_info = driver_manager.get_device_info()
        print(f"   ✅ 驱动管理器正常，设备信息: {device_info.get('brand', 'Unknown')}")
        
        # 测试文件推送器
        from tools.file_pusher import FilePusher
        pusher = FilePusher()
        connection_ok = pusher.check_device_connection()
        print(f"   ✅ 文件推送器正常，设备连接: {'成功' if connection_ok else '失败'}")
        
        # 测试配置功能
        from config.multimodal_config import get_timeout
        timeout = get_timeout("file_selection")
        print(f"   ✅ 配置功能正常，文件选择超时: {timeout}秒")
        
        print("\n🎉 基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def test_ella_page_creation():
    """测试Ella页面创建"""
    print("\n📱 测试Ella页面创建...")
    
    try:
        from pages.apps.ella.dialogue_page import EllaDialoguePage
        
        # 创建页面实例
        ella_page = EllaDialoguePage()
        print("   ✅ EllaDialoguePage 实例创建成功")
        
        # 检查多模态处理器
        if hasattr(ella_page, 'multimodal_handler'):
            print("   ✅ 多模态处理器已初始化")
        else:
            print("   ❌ 多模态处理器未初始化")
            return False
        
        # 检查文件推送器
        if hasattr(ella_page.multimodal_handler, '_prepare_test_documents'):
            print("   ✅ 文件推送功能已集成")
        else:
            print("   ❌ 文件推送功能未集成")
            return False
        
        print("\n🎉 Ella页面创建测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ Ella页面创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 导入修复验证工具")
    print("="*50)
    
    results = {
        "导入测试": test_imports(),
        "基本功能测试": test_basic_functionality(),
        "Ella页面创建测试": test_ella_page_creation()
    }
    
    print("\n📊 测试结果汇总:")
    print("="*30)
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for result in results.values() if result)
    total_count = len(results)
    
    if success_count == total_count:
        print(f"\n🎉 所有测试通过！({success_count}/{total_count})")
        print("导入问题已修复，可以正常使用多模态功能。")
    else:
        print(f"\n⚠️ 部分测试失败 ({success_count}/{total_count})")
        print("请检查失败的测试项目并修复相关问题。")

if __name__ == "__main__":
    main()
