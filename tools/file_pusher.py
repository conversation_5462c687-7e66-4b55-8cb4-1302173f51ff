"""
文件推送工具
用于将测试文件推送到Android设备
"""
import os
import subprocess
import time
from pathlib import Path
from typing import List, Optional
from core.logger import log


class FilePusher:
    """文件推送工具类"""
    
    def __init__(self):
        """初始化文件推送工具"""
        self.project_root = Path(__file__).parent.parent
        self.static_data_path = self.project_root / "data" / "static"
        
    def push_documents_to_device(self, target_path: str = "/sdcard/Download") -> bool:
        """
        推送文档文件到设备
        
        Args:
            target_path: 目标路径，默认为/sdcard/Download
            
        Returns:
            bool: 推送是否成功
        """
        try:
            log.info("开始推送文档文件到设备")
            
            document_folder = self.static_data_path / "document"
            if not document_folder.exists():
                log.error(f"文档文件夹不存在: {document_folder}")
                return False
            
            # 获取所有文档文件
            document_files = self._get_document_files(document_folder)
            if not document_files:
                log.warning("未找到任何文档文件")
                return True
            
            # 确保目标目录存在
            if not self._ensure_target_directory(target_path):
                log.error(f"无法创建目标目录: {target_path}")
                return False
            
            # 推送每个文件
            success_count = 0
            for file_path in document_files:
                if self._push_single_file(file_path, target_path):
                    success_count += 1
                else:
                    log.warning(f"推送文件失败: {file_path}")
            
            log.info(f"文档推送完成: {success_count}/{len(document_files)} 个文件成功")
            return success_count > 0
            
        except Exception as e:
            log.error(f"推送文档文件失败: {e}")
            return False
    
    def push_images_to_device(self, target_path: str = "/sdcard/Pictures") -> bool:
        """
        推送图片文件到设备
        
        Args:
            target_path: 目标路径，默认为/sdcard/Pictures
            
        Returns:
            bool: 推送是否成功
        """
        try:
            log.info("开始推送图片文件到设备")
            
            gallery_folder = self.static_data_path / "gallery"
            if not gallery_folder.exists():
                log.error(f"图片文件夹不存在: {gallery_folder}")
                return False
            
            # 获取所有图片文件
            image_files = self._get_image_files(gallery_folder)
            if not image_files:
                log.warning("未找到任何图片文件")
                return True
            
            # 确保目标目录存在
            if not self._ensure_target_directory(target_path):
                log.error(f"无法创建目标目录: {target_path}")
                return False
            
            # 推送每个文件
            success_count = 0
            for file_path in image_files:
                if self._push_single_file(file_path, target_path):
                    success_count += 1
                else:
                    log.warning(f"推送文件失败: {file_path}")
            
            log.info(f"图片推送完成: {success_count}/{len(image_files)} 个文件成功")
            return success_count > 0
            
        except Exception as e:
            log.error(f"推送图片文件失败: {e}")
            return False
    
    def _get_document_files(self, folder_path: Path) -> List[Path]:
        """获取文档文件列表"""
        document_extensions = ['.txt', '.pdf', '.doc', '.docx', '.rtf', '.odt']
        files = []
        
        for ext in document_extensions:
            files.extend(folder_path.glob(f"*{ext}"))
            files.extend(folder_path.glob(f"*{ext.upper()}"))
        
        return files
    
    def _get_image_files(self, folder_path: Path) -> List[Path]:
        """获取图片文件列表"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        files = []
        
        for ext in image_extensions:
            files.extend(folder_path.glob(f"*{ext}"))
            files.extend(folder_path.glob(f"*{ext.upper()}"))
        
        return files
    
    def _ensure_target_directory(self, target_path: str) -> bool:
        """确保目标目录存在"""
        try:
            cmd = f"adb shell mkdir -p {target_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                log.debug(f"目标目录已确保存在: {target_path}")
                return True
            else:
                log.error(f"创建目标目录失败: {result.stderr}")
                return False
                
        except Exception as e:
            log.error(f"确保目标目录存在时出错: {e}")
            return False
    
    def _push_single_file(self, file_path: Path, target_path: str) -> bool:
        """推送单个文件"""
        try:
            target_file = f"{target_path}/{file_path.name}"
            cmd = f'adb push "{file_path}" "{target_file}"'
            
            log.debug(f"推送文件: {file_path.name} -> {target_file}")
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                log.info(f"✅ 文件推送成功: {file_path.name}")
                return True
            else:
                log.error(f"❌ 文件推送失败: {file_path.name}, 错误: {result.stderr}")
                return False
                
        except Exception as e:
            log.error(f"推送文件时出错: {e}")
            return False
    
    def check_device_connection(self) -> bool:
        """检查设备连接状态"""
        try:
            result = subprocess.run("adb devices", shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                devices = [line for line in lines[1:] if line.strip() and 'device' in line]
                
                if devices:
                    log.info(f"检测到 {len(devices)} 个连接的设备")
                    return True
                else:
                    log.error("未检测到连接的设备")
                    return False
            else:
                log.error(f"检查设备连接失败: {result.stderr}")
                return False
                
        except Exception as e:
            log.error(f"检查设备连接时出错: {e}")
            return False
    
    def clean_target_directory(self, target_path: str, file_pattern: str = "*") -> bool:
        """清理目标目录中的文件"""
        try:
            cmd = f"adb shell rm -f {target_path}/{file_pattern}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                log.info(f"✅ 清理目标目录成功: {target_path}")
                return True
            else:
                log.warning(f"清理目标目录可能失败: {result.stderr}")
                return True  # 即使失败也继续，可能是目录不存在
                
        except Exception as e:
            log.error(f"清理目标目录时出错: {e}")
            return False
    
    def list_device_files(self, target_path: str) -> List[str]:
        """列出设备目录中的文件"""
        try:
            cmd = f"adb shell ls {target_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                files = [f.strip() for f in result.stdout.split('\n') if f.strip()]
                log.debug(f"设备目录 {target_path} 中的文件: {files}")
                return files
            else:
                log.warning(f"列出设备文件失败: {result.stderr}")
                return []
                
        except Exception as e:
            log.error(f"列出设备文件时出错: {e}")
            return []


# 全局实例
file_pusher = FilePusher()


def push_test_documents(target_path: str = "/sdcard/Download") -> bool:
    """推送测试文档的便捷函数"""
    return file_pusher.push_documents_to_device(target_path)


def push_test_images(target_path: str = "/sdcard/Pictures") -> bool:
    """推送测试图片的便捷函数"""
    return file_pusher.push_images_to_device(target_path)


if __name__ == "__main__":
    # 测试文件推送功能
    pusher = FilePusher()
    
    # 检查设备连接
    if pusher.check_device_connection():
        # 推送文档
        pusher.push_documents_to_device()
        
        # 推送图片
        pusher.push_images_to_device()
    else:
        print("请确保设备已连接并启用USB调试")
