"""
多模态功能配置文件
定义各种多模态功能的配置参数
"""

# 多模态功能超时配置
MULTIMODAL_TIMEOUTS = {
    "entrance_open": 3,          # 多模态入口打开超时
    "button_click": 2,           # 按钮点击后等待时间
    "app_launch": 5,             # 应用启动超时
    "file_selection": 10,        # 文件选择超时
    "photo_selection": 8,        # 照片选择超时
    "camera_ready": 5,           # 相机准备就绪超时
    "photo_capture": 3,          # 拍照超时
    "photo_confirm": 3,          # 照片确认超时
    "return_dialogue": 10,       # 返回对话页面超时
    "ai_generator_setup": 5,     # AI生图设置超时
    "file_push": 10,             # 文件推送超时
    "file_sync": 2               # 文件系统同步等待时间
}

# 多模态功能重试配置
MULTIMODAL_RETRIES = {
    "entrance_open": 2,          # 多模态入口打开重试次数
    "button_click": 1,           # 按钮点击重试次数
    "file_selection": 2,         # 文件选择重试次数
    "photo_selection": 2,        # 照片选择重试次数
    "camera_operation": 3,       # 相机操作重试次数
    "return_dialogue": 3         # 返回对话页面重试次数
}

# 文件选择器配置
FILE_SELECTOR_CONFIG = {
    "supported_extensions": [".pdf", ".doc", ".docx", ".txt", ".jpg", ".png", ".jpeg"],
    "default_selection_method": "first_available",  # first_available, by_name, by_type
    "confirm_buttons": ["确定", "OK", "选择", "Select", "完成", "Done", "打开", "Open", "选取", "Choose"],
    "cancel_buttons": ["取消", "Cancel", "返回", "Back"],
    "target_paths": {
        "documents": "/sdcard/Download",
        "images": "/sdcard/Pictures",
        "dcim": "/sdcard/DCIM/Camera"
    },
    "selection_strategies": [
        "specific_file",      # 选择特定文件
        "document_file",      # 选择文档文件
        "generic_file",       # 通用文件选择
        "confirm_default"     # 确认默认选择
    ]
}

# 图库选择器配置
GALLERY_SELECTOR_CONFIG = {
    "supported_formats": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"],
    "selection_method": "first_available",  # first_available, random, latest
    "confirm_buttons": ["确定", "OK", "选择", "Select", "完成", "Done", "使用", "Use", "应用", "Apply"],
    "cancel_buttons": ["取消", "Cancel", "返回", "Back"],
    "max_photos_to_check": 10,   # 最多检查的照片数量
    "min_image_size": 100,       # 最小图片尺寸（像素）
    "selection_strategies": [
        "specific_image",        # 选择特定图片
        "any_image",            # 选择任何图片
        "recyclerview_image",   # 从RecyclerView选择
        "confirm_default"       # 确认默认选择
    ]
}

# 相机配置
CAMERA_CONFIG = {
    "shutter_button_selectors": [
        {"resourceId": "com.transsion.camera:id/shutter_button"},
        {"description": "拍照"},
        {"description": "Shutter"},
        {"description": "Take photo"},
        {"resourceId": "com.android.camera:id/shutter_button"},
        {"className": "android.widget.ImageButton"}
    ],
    "confirm_button_selectors": [
        {"resourceId": "com.transsion.camera:id/done_button"},
        {"description": "确认"},
        {"description": "Confirm"},
        {"description": "Done"},
        {"text": "确定"},
        {"text": "OK"},
        {"text": "✓"},
        {"resourceId": "com.android.camera:id/done_button"},
        {"resourceId": "com.transsion.camera:id/done_button"},
    ],
    "fallback_coordinates": {
        "shutter_position": (0.5, 0.85),    # 屏幕比例坐标 (x, y)
        "confirm_position": (0.8, 0.1)      # 确认按钮通常在右上角
    }
}

# AI生图配置
AI_IMAGE_GENERATOR_CONFIG = {
    "type_options": [
        "写实风格",
        "卡通风格",
        "油画风格",
        "水彩风格",
        "素描风格"
    ],
    "scale_options": [
        "1:1",
        "16:9",
        "9:16",
        "4:3",
        "3:4"
    ],
    "scale_option_texts": [
        "1:1", "16:9", "9:16", "4:3", "3:4",
        "正方形", "横屏", "竖屏", "宽屏", "标准"
    ],
    "scale_popup_selectors": [
        {"className": "android.view.View", "clickable": True},
        {"className": "android.widget.TextView", "clickable": True},
        {"descriptionContains": "比例"},
        {"descriptionContains": "scale"},
        {"descriptionContains": "ratio"}
    ],
    "default_type": "写实风格",
    "default_scale": "1:1",
    "popup_wait_time": 2,  # 等待弹窗出现的时间
    "selection_strategies": [
        "scale_enum_element",    # 通过scale_enum元素选择
        "scale_popup",          # 从弹窗中选择
        "scale_by_text",        # 通过文本选择
        "any_scale_option"      # 选择任何可用选项
    ]
}

# 多模态功能映射
MULTIMODAL_FUNCTION_MAP = {
    "document": {
        "name": "文档选择",
        "description": "选择并上传文档文件",
        "element_key": "multi_modal_document",
        "expected_apps": ["文件管理器", "Documents", "Files"]
    },
    "gallery": {
        "name": "图库选择", 
        "description": "从图库选择图片",
        "element_key": "multi_modal_gallery",
        "expected_apps": ["图库", "Gallery", "Photos", "AI Gallery"]
    },
    "camera": {
        "name": "相机拍照",
        "description": "使用相机拍摄照片",
        "element_key": "multi_modal_camera", 
        "expected_apps": ["相机", "Camera"]
    },
    "ai_image_generator": {
        "name": "AI生图",
        "description": "使用AI生成图像",
        "element_key": "multi_modal_ai_image_generator",
        "expected_apps": []  # 通常在Ella内部处理
    }
}

# 错误处理配置
ERROR_HANDLING_CONFIG = {
    "continue_on_selection_failure": True,   # 选择失败时是否继续流程
    "continue_on_camera_operation": True,    # 相机操作失败时是否继续
    "continue_on_app_launch_failure": False, # 应用启动失败时是否继续
    "screenshot_on_error": True,             # 错误时是否截图
    "log_detailed_errors": True,             # 是否记录详细错误信息
    "auto_return_on_completion": False       # 完成后是否自动返回dialogue页面
}

# 调试配置
DEBUG_CONFIG = {
    "enable_step_screenshots": False,        # 是否在每个步骤截图
    "enable_element_highlighting": False,    # 是否高亮元素
    "log_element_properties": False,         # 是否记录元素属性
    "pause_between_steps": 0                 # 步骤间暂停时间(秒)
}

# 兼容性配置
COMPATIBILITY_CONFIG = {
    "cm5_specific_elements": {
        "send_button_alt": {"xpath": "(//android.widget.FrameLayout)[11]"}
    },
    "brand_specific_apps": {
        "transsion": {
            "camera": "com.transsion.camera",
            "gallery": "com.transsion.aigallery", 
            "file_manager": "com.transsion.filemanager"
        },
        "android": {
            "camera": "com.android.camera",
            "gallery": "com.android.gallery3d",
            "file_manager": "com.android.documentsui"
        }
    }
}

# 获取配置的辅助函数
def get_timeout(operation: str) -> int:
    """获取指定操作的超时时间"""
    return MULTIMODAL_TIMEOUTS.get(operation, 5)

def get_retry_count(operation: str) -> int:
    """获取指定操作的重试次数"""
    return MULTIMODAL_RETRIES.get(operation, 1)

def get_function_config(function_name: str) -> dict:
    """获取指定功能的配置"""
    return MULTIMODAL_FUNCTION_MAP.get(function_name, {})

def get_camera_shutter_selectors() -> list:
    """获取相机快门按钮选择器列表"""
    return CAMERA_CONFIG["shutter_button_selectors"]

def get_camera_confirm_selectors() -> list:
    """获取相机确认按钮选择器列表"""
    return CAMERA_CONFIG["confirm_button_selectors"]

def get_file_confirm_buttons() -> list:
    """获取文件选择确认按钮列表"""
    return FILE_SELECTOR_CONFIG["confirm_buttons"]

def get_gallery_confirm_buttons() -> list:
    """获取图库选择确认按钮列表"""
    return GALLERY_SELECTOR_CONFIG["confirm_buttons"]

def is_debug_enabled(feature: str) -> bool:
    """检查指定调试功能是否启用"""
    return DEBUG_CONFIG.get(feature, False)

def should_continue_on_error(error_type: str) -> bool:
    """检查在指定错误类型时是否应该继续执行"""
    return ERROR_HANDLING_CONFIG.get(f"continue_on_{error_type}", False)

def get_ai_image_scale_options() -> list:
    """获取AI生图比例选项列表"""
    return AI_IMAGE_GENERATOR_CONFIG.get("scale_option_texts", [])

def get_ai_image_scale_selectors() -> list:
    """获取AI生图比例选择器列表"""
    return AI_IMAGE_GENERATOR_CONFIG.get("scale_popup_selectors", [])

def get_ai_image_config(key: str):
    """获取AI生图配置项"""
    return AI_IMAGE_GENERATOR_CONFIG.get(key)
