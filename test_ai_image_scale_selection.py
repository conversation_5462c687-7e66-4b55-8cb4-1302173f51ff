"""
测试AI生图比例选择功能
验证比例选择浮窗的处理逻辑
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log


def test_ai_image_generator_complete_flow():
    """测试AI生图完整流程"""
    log.info("=== 测试AI生图完整流程 ===")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            log.error("启动Ella应用失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 确保在对话页面
        if not ella_page.ensure_on_chat_page():
            log.error("无法确保在对话页面")
            return False
        
        log.info("开始执行AI生图功能...")
        
        # 执行AI生图功能（包含比例选择）
        result = ella_page.execute_multimodal_function("ai_image_generator")
        
        if result:
            log.info("✅ AI生图功能测试成功")
        else:
            log.error("❌ AI生图功能测试失败")
        
        return result
        
    except Exception as e:
        log.error(f"AI生图功能测试异常: {e}")
        return False


def test_ai_image_scale_selection_only():
    """仅测试AI生图比例选择"""
    log.info("=== 测试AI生图比例选择 ===")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            log.error("启动Ella应用失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 确保在对话页面
        if not ella_page.ensure_on_chat_page():
            log.error("无法确保在对话页面")
            return False
        
        log.info("开始测试比例选择...")
        
        # 打开多模态入口
        if not ella_page.multimodal_handler._open_multimodal_entrance():
            log.error("打开多模态入口失败")
            return False
        
        # 点击AI生图按钮
        ai_generator_button = ella_page.page_elements.get('multi_modal_ai_image_generator')
        if not ai_generator_button or not ai_generator_button.is_exists():
            log.error("未找到AI生图按钮")
            return False
        
        ai_generator_button.click()
        time.sleep(2)
        log.info("✅ 点击AI生图按钮成功")
        
        # 点击比例按钮
        scale_button = ella_page.page_elements.get('multi_modal_ai_image_generator_scale')
        if not scale_button or not scale_button.is_exists():
            log.error("未找到比例按钮")
            return False
        
        scale_button.click()
        time.sleep(2)
        log.info("✅ 点击比例按钮成功")
        
        # 测试比例选择
        result = ella_page.multimodal_handler._select_ai_image_scale_option()
        
        if result:
            log.info("✅ 比例选择测试成功")
        else:
            log.error("❌ 比例选择测试失败")
        
        return result
        
    except Exception as e:
        log.error(f"比例选择测试异常: {e}")
        return False


def interactive_ai_image_test():
    """交互式AI生图测试"""
    print("\n🎨 交互式AI生图测试")
    print("="*50)
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        print("1. 启动Ella应用...")
        if not ella_page.start_app():
            print("❌ 启动失败")
            return False
        print("✅ 启动成功")
        
        # 等待页面加载
        print("2. 等待页面加载...")
        if not ella_page.wait_for_page_load():
            print("❌ 页面加载失败")
            return False
        print("✅ 页面加载成功")
        
        while True:
            print("\n请选择测试功能:")
            print("1. 测试完整AI生图流程")
            print("2. 仅测试比例选择")
            print("3. 手动导航测试")
            print("4. 退出")
            
            choice = input("请输入选择 (1-4): ").strip()
            
            if choice == "1":
                print("\n开始测试完整AI生图流程...")
                result = ella_page.execute_multimodal_function("ai_image_generator")
                print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
                
            elif choice == "2":
                print("\n开始测试比例选择...")
                result = test_ai_image_scale_selection_only()
                print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
                
            elif choice == "3":
                print("\n手动导航测试:")
                print("请手动导航到AI生图比例选择页面，然后按回车键测试比例选择...")
                input()
                result = ella_page.multimodal_handler._select_ai_image_scale_option()
                print(f"比例选择结果: {'✅ 成功' if result else '❌ 失败'}")
                
            elif choice == "4":
                print("退出测试")
                break
                
            else:
                print("无效选择，请重新输入")
        
        return True
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
        return False
    except Exception as e:
        print(f"交互式测试异常: {e}")
        return False


def test_scale_popup_analysis():
    """测试比例选择浮窗分析"""
    print("\n🔍 比例选择浮窗分析")
    print("="*50)
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app():
            print("❌ 启动失败")
            return False
        
        print("请手动导航到AI生图比例选择页面，点击比例按钮后按回车键开始分析...")
        input()
        
        # 分析当前页面元素
        print("分析页面元素...")
        
        # 查找android.view.View元素
        views = ella_page.driver(className="android.view.View")
        if views.exists():
            print(f"找到 {len(views)} 个android.view.View元素")
            for i in range(min(len(views), 10)):
                try:
                    view = views[i]
                    bounds = view.info.get('bounds', {})
                    clickable = view.info.get('clickable', False)
                    print(f"  View {i+1}: clickable={clickable}, bounds={bounds}")
                except:
                    continue
        
        # 查找可点击元素
        clickable_elements = ella_page.driver(clickable=True)
        if clickable_elements.exists():
            print(f"\n找到 {len(clickable_elements)} 个可点击元素")
            for i in range(min(len(clickable_elements), 10)):
                try:
                    element = clickable_elements[i]
                    class_name = element.info.get('className', '')
                    text = element.get_text() if hasattr(element, 'get_text') else ''
                    print(f"  Element {i+1}: {class_name}, text='{text}'")
                except:
                    continue
        
        # 查找TextView元素
        text_views = ella_page.driver(className="android.widget.TextView")
        if text_views.exists():
            print(f"\n找到 {len(text_views)} 个TextView元素")
            for i in range(min(len(text_views), 10)):
                try:
                    text = text_views[i].get_text()
                    if text and any(char in text for char in [":", "×", "x", "1", "9", "16", "4", "3"]):
                        print(f"  可能的比例选项: '{text}'")
                except:
                    continue
        
        return True
        
    except Exception as e:
        print(f"浮窗分析失败: {e}")
        return False


def main():
    """主函数"""
    print("🎨 AI生图比例选择测试工具")
    print("="*50)
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 完整AI生图流程测试")
    print("2. 仅测试比例选择")
    print("3. 交互式测试")
    print("4. 浮窗分析")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    try:
        if choice == "1":
            # 完整流程测试
            result = test_ai_image_generator_complete_flow()
            print(f"\nAI生图完整流程测试结果: {'✅ 成功' if result else '❌ 失败'}")
            
        elif choice == "2":
            # 仅测试比例选择
            result = test_ai_image_scale_selection_only()
            print(f"\n比例选择测试结果: {'✅ 成功' if result else '❌ 失败'}")
            
        elif choice == "3":
            # 交互式测试
            interactive_ai_image_test()
            
        elif choice == "4":
            # 浮窗分析
            test_scale_popup_analysis()
            
        else:
            print("无效选择")
    
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        log.error(f"测试异常: {e}")


if __name__ == "__main__":
    main()
