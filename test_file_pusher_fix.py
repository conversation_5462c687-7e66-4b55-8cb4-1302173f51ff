"""
测试文件推送修复
验证编码问题是否已解决
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from tools.file_pusher import FilePusher
from core.logger import log


def test_encoding_fix():
    """测试编码修复"""
    log.info("开始测试文件推送编码修复...")
    
    pusher = FilePusher()
    
    # 测试设备连接
    log.info("1. 测试设备连接...")
    if pusher.check_device_connection():
        log.info("✅ 设备连接检查通过")
    else:
        log.error("❌ 设备连接失败")
        return False
    
    # 测试目录创建
    log.info("2. 测试目录创建...")
    if pusher._ensure_target_directory("/sdcard/Download"):
        log.info("✅ 目录创建成功")
    else:
        log.error("❌ 目录创建失败")
        return False
    
    # 测试文件列表
    log.info("3. 测试文件列表...")
    files = pusher.list_device_files("/sdcard/Download")
    log.info(f"✅ 文件列表获取成功，共 {len(files)} 个文件")
    
    # 测试文档推送
    log.info("4. 测试文档推送...")
    if pusher.push_documents_to_device("/sdcard/Download"):
        log.info("✅ 文档推送成功")
    else:
        log.error("❌ 文档推送失败")
        return False
    
    # 验证推送结果
    log.info("5. 验证推送结果...")
    files_after = pusher.list_device_files("/sdcard/Download")
    doc_files = [f for f in files_after if f.endswith('.txt')]
    
    if doc_files:
        log.info(f"✅ 验证成功，找到 {len(doc_files)} 个文档文件: {doc_files}")
    else:
        log.warning("⚠️ 未找到推送的文档文件")
    
    log.info("🎉 编码修复测试完成！")
    return True


if __name__ == "__main__":
    try:
        success = test_encoding_fix()
        if success:
            print("\n🎉 测试成功！编码问题已修复。")
        else:
            print("\n❌ 测试失败，请检查设备连接和ADB配置。")
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {e}")
        log.error(f"测试异常: {e}")
